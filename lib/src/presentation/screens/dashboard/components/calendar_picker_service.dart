import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import '../../../../core/config/responsive.dart';
import '../../../../core/enum/user_role.dart';
import '../../../cubit/top_performers/top_performers_cubit.dart';
import '../../../cubit/user/user_cubit.dart';
import 'month_year_picker.dart';

class CalendarPickerService {
  static void showMonthYearPicker({
    required BuildContext anchorContext,
    required ValueNotifier<String> selectMonth,
    required TopPerformersCubit topPerformersCubit,
    required BuildContext parentContext,
  }) {
    final DateTime initialDate = DateTime.parse('${selectMonth.value}-01');
    final bool isMobile = Responsive.isMobile(parentContext);
    final bool isTablet = Responsive.isTablet(parentContext);

    if (isMobile || isTablet) {
      _showResponsiveDialog(
        parentContext: parentContext,
        initialDate: initialDate,
        selectMonth: selectMonth,
        topPerformersCubit: topPerformersCubit,
      );
    } else {
      _showOverlayPicker(
        anchorContext: anchorContext,
        parentContext: parentContext,
        initialDate: initialDate,
        selectMonth: selectMonth,
        topPerformersCubit: topPerformersCubit,
      );
    }
  }

  static void _showResponsiveDialog({
    required BuildContext parentContext,
    required DateTime initialDate,
    required ValueNotifier<String> selectMonth,
    required TopPerformersCubit topPerformersCubit,
  }) {
    final bool isMobile = Responsive.isMobile(parentContext);
    
    showDialog(
      context: parentContext,
      barrierDismissible: true,
      barrierColor: Colors.black.withValues(alpha: 0.3),
      builder: (BuildContext dialogContext) {
        return Dialog(
          backgroundColor: Colors.transparent,
          elevation: 0,
          insetPadding: EdgeInsets.all(isMobile ? 16 : 24),
          child: Center(
            child: ConstrainedBox(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(dialogContext).size.width - 32,
                maxHeight: MediaQuery.of(dialogContext).size.height - 100,
              ),
              child: MonthYearPicker(
                anchorKey: GlobalKey(),
                initialDate: initialDate,
                onDateSelected: (DateTime date) async {
                  await _handleDateSelection(
                    date: date,
                    selectMonth: selectMonth,
                    topPerformersCubit: topPerformersCubit,
                    parentContext: parentContext,
                  );
                  
                  if (dialogContext.mounted) {
                    Navigator.of(dialogContext).pop();
                  }
                },
                onCancel: () {
                  if (dialogContext.mounted) {
                    Navigator.of(dialogContext).pop();
                  }
                },
              ),
            ),
          ),
        );
      },
    );
  }

  static void _showOverlayPicker({
    required BuildContext anchorContext,
    required BuildContext parentContext,
    required DateTime initialDate,
    required ValueNotifier<String> selectMonth,
    required TopPerformersCubit topPerformersCubit,
  }) {
    final mediaQuery = MediaQuery.of(parentContext);
    final screenWidth = mediaQuery.size.width;
    final screenHeight = mediaQuery.size.height;

    final RenderBox? renderBox = anchorContext.findRenderObject() as RenderBox?;
    if (renderBox == null) return;

    final Offset offset = renderBox.localToGlobal(Offset.zero);
    final Size size = renderBox.size;
    final double padding = 16;
    final double pickerHeight = 380;
    final double pickerWidth = _calculatePickerWidth(screenWidth);

    // Calculate optimal position with screen bounds checking
    final currentPosition = _calculateOptimalPosition(
      anchorOffset: offset,
      anchorSize: size,
      screenWidth: screenWidth,
      screenHeight: screenHeight,
      pickerWidth: pickerWidth,
      pickerHeight: pickerHeight,
      padding: padding,
    );

    OverlayEntry? entry;
    entry = OverlayEntry(
      builder: (context) => Material(
        type: MaterialType.transparency,
        child: Stack(
          children: [
            // Background overlay
            Positioned.fill(
              child: GestureDetector(
                onTap: () {
                  entry?.remove();
                },
                child: Container(color: Colors.black.withValues(alpha: 0.3)),
              ),
            ),
            // Calendar picker with responsive positioning
            Positioned(
              left: currentPosition.left,
              top: currentPosition.top,
              child: Material(
                elevation: 8.0,
                borderRadius: BorderRadius.circular(8.0),
                child: MonthYearPicker(
                  anchorKey: GlobalKey(),
                  initialDate: initialDate,
                  onDateSelected: (DateTime date) async {
                    await _handleDateSelection(
                      date: date,
                      selectMonth: selectMonth,
                      topPerformersCubit: topPerformersCubit,
                      parentContext: parentContext,
                    );
                    entry?.remove();
                  },
                  onCancel: () {
                    entry?.remove();
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );

    // Insert the overlay
    final overlay = Overlay.of(parentContext);
    overlay.insert(entry);
  }

  static Future<void> _handleDateSelection({
    required DateTime date,
    required ValueNotifier<String> selectMonth,
    required TopPerformersCubit topPerformersCubit,
    required BuildContext parentContext,
  }) async {
    final formattedDate = DateFormat('yyyy-MM').format(date);
    selectMonth.value = formattedDate;
    
    // Check if the widget is still mounted before using context
    if (!parentContext.mounted) return;
    
    final userRole = parentContext.read<UserCubit>().state.user?.role;
    
    if (userRole == UserRole.agent || userRole == UserRole.brokerage) {
      await topPerformersCubit.getAgentTopPerformers(formattedDate);
    } else if (userRole == UserRole.admin || userRole == UserRole.platformOwner) {
      await topPerformersCubit.getBrokerageTopPerformers(formattedDate);
      await topPerformersCubit.getAgentTopPerformers(formattedDate);
    }
  }

  static double _calculatePickerWidth(double screenWidth) {
    if (screenWidth <= 480) {
      return (screenWidth - 40).clamp(280, screenWidth - 20);
    } else if (screenWidth <= 800) {
      return (screenWidth * 0.85).clamp(300, 450);
    } else {
      return 350;
    }
  }

  static PickerPosition _calculateOptimalPosition({
    required Offset anchorOffset,
    required Size anchorSize,
    required double screenWidth,
    required double screenHeight,
    required double pickerWidth,
    required double pickerHeight,
    required double padding,
  }) {
    double top = anchorOffset.dy + anchorSize.height + 8;
    double left;
    
    // Calculate horizontal position
    final double rightAlignedLeft = anchorOffset.dx + anchorSize.width - pickerWidth;
    final double minLeft = padding;
    final double maxLeft = screenWidth - pickerWidth - padding;
    
    if (rightAlignedLeft >= minLeft && rightAlignedLeft <= maxLeft) {
      left = rightAlignedLeft;
    } else if (rightAlignedLeft > maxLeft && maxLeft >= minLeft) {
      left = maxLeft;
    } else {
      final double leftAlignedLeft = anchorOffset.dx;
      if (leftAlignedLeft >= minLeft && leftAlignedLeft <= maxLeft) {
        left = leftAlignedLeft;
      } else {
        left = leftAlignedLeft.clamp(minLeft, maxLeft);
      }
    }
    
    left = left.clamp(minLeft, maxLeft.clamp(minLeft, screenWidth - padding));
    
    // Calculate vertical position with screen bounds checking
    if (top + pickerHeight > screenHeight - padding) {
      final double topPosition = anchorOffset.dy - pickerHeight - 8;
      if (topPosition >= padding) {
        top = topPosition;
      } else {
        // If neither above nor below fits, center vertically
        top = (screenHeight - pickerHeight) / 2;
        top = top.clamp(padding, screenHeight - pickerHeight - padding);
      }
    }
    
    // Ensure picker doesn't go off-screen vertically
    top = top.clamp(padding, screenHeight - pickerHeight - padding);
    
    return PickerPosition(left: left, top: top);
  }
}

class PickerPosition {
  final double left;
  final double top;

  const PickerPosition({required this.left, required this.top});
}
