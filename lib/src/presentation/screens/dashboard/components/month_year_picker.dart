import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../../core/config/responsive.dart';

class PickerPosition {
  final double left;
  final double top;

  const PickerPosition({required this.left, required this.top});
}

class MonthYearPicker extends StatefulWidget {
  final DateTime initialDate;
  final ValueChanged<DateTime> onDateSelected;
  final VoidCallback onCancel;
  final GlobalKey anchorKey;

  const MonthYearPicker({
    super.key,
    required this.initialDate,
    required this.onDateSelected,
    required this.onCancel,
    required this.anchorKey,
  });

  @override
  State<MonthYearPicker> createState() => _MonthYearPickerState();
}

class _MonthYearPickerState extends State<MonthYearPicker> {
  late int selectedYear;
  late int selectedMonth;
  bool showingYears = false;
  int yearPageIndex = 0;

  final DateTime today = DateTime.now();
  final DateTime firstDate = DateTime(2015, 1, 1);
  late final DateTime lastDate = DateTime(today.year, 12, 31);

  @override
  void initState() {
    super.initState();
    selectedYear = widget.initialDate.year.clamp(firstDate.year, today.year);
    selectedMonth = widget.initialDate.month;
    if (selectedYear == today.year && selectedMonth > today.month) {
      selectedMonth = today.month;
    }
    final yearIndex = selectedYear - firstDate.year;
    yearPageIndex = yearIndex ~/ 12;
  }

  @override
  Widget build(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;
    final double screenHeight = MediaQuery.of(context).size.height;
    final double pickerWidth = _calculatePickerWidth(screenWidth);
    final double pickerHeight = _calculatePickerHeight(screenHeight);
    final bool isMobile = Responsive.isMobile(context);
    final bool isTablet = Responsive.isTablet(context);
    
    return Theme(
      data: Theme.of(context).copyWith(
        colorScheme: ColorScheme.light(
          primary: Colors.blue,
          onPrimary: Colors.white,
          surface: Colors.white,
          onSurface: Colors.black,
        ),
      ),
      child: Material(
        elevation: 4.0,
        borderRadius: BorderRadius.circular(8.0),
        color: Colors.white,
        child: Container(
          width: pickerWidth,
          height: pickerHeight,
          constraints: BoxConstraints(
            maxWidth: screenWidth - 32,
            maxHeight: screenHeight - 100,
          ),
          child: Column(
            children: [
              _buildHeader(),
              const SizedBox(height: 12),
              Expanded(
                child: showingYears 
                    ? _buildYearGrid(isMobile, isTablet) 
                    : _buildMonthGrid(isMobile, isTablet),
              ),
              const SizedBox(height: 8),
              _buildActionButtons(isMobile),
              const SizedBox(height: 12),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(8.0),
          topRight: Radius.circular(8.0),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            onPressed: () {
              setState(() {
                if (showingYears) {
                  if (yearPageIndex > 0) {
                    yearPageIndex--;
                  }
                } else {
                  selectedYear = (selectedYear - 1)
                      .clamp(firstDate.year, lastDate.year)
                      .toInt();
                }
              });
            },
            icon: Icon(Icons.chevron_left, color: Colors.black),
          ),
          GestureDetector(
            onTap: () {
              setState(() {
                showingYears = !showingYears;
                if (showingYears) {
                  final yearIndex = selectedYear - firstDate.year;
                  yearPageIndex = yearIndex ~/ 12;
                }
              });
            },
            child: Text(
              showingYears ? _getYearPageRangeText() : selectedYear.toString(),
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black,
              ),
            ),
          ),
          IconButton(
            onPressed: () {
              setState(() {
                if (showingYears) {
                  final totalYears = lastDate.year - firstDate.year + 1;
                  final maxPageIndex = (totalYears - 1) ~/ 12;
                  if (yearPageIndex < maxPageIndex) {
                    yearPageIndex++;
                  }
                } else {
                  selectedYear = (selectedYear + 1)
                      .clamp(firstDate.year, lastDate.year)
                      .toInt();
                }
              });
            },
            icon: Icon(Icons.chevron_right, color: Colors.black),
          ),
        ],
      ),
    );
  }

  String _getYearPageRangeText() {
    final startYear = firstDate.year + (yearPageIndex * 12);
    final totalYears = lastDate.year - firstDate.year + 1;
    final remainingYears = totalYears - (yearPageIndex * 12);
    final endYear = startYear + (remainingYears > 12 ? 11 : remainingYears - 1);
    return '$startYear - $endYear';
  }

  Widget _buildYearGrid(bool isMobile, bool isTablet) {
    final startYear = firstDate.year + (yearPageIndex * 12);
    final totalYears = lastDate.year - firstDate.year + 1;
    final remainingYears = totalYears - (yearPageIndex * 12);
    final yearsToShow = remainingYears > 12 ? 12 : remainingYears;

    List<int> years = [];
    for (int i = 0; i < yearsToShow; i++) {
      years.add(startYear + i);
    }

    // Responsive grid configuration
    int crossAxisCount = 3;
    double childAspectRatio = 2.8;
    
    if (isMobile) {
      crossAxisCount = 2;
      childAspectRatio = 2.5;
    } else if (isTablet) {
      crossAxisCount = 3;
      childAspectRatio = 2.6;
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: crossAxisCount,
          childAspectRatio: childAspectRatio,
          crossAxisSpacing: 4,
          mainAxisSpacing: 4,
        ),
        shrinkWrap: true,
        physics: NeverScrollableScrollPhysics(),
        itemCount: years.length,
        itemBuilder: (context, index) {
          final year = years[index];
          final isSelected = year == selectedYear;

          return GestureDetector(
            onTap: () {
              setState(() {
                selectedYear = year;
                if (selectedYear == today.year && selectedMonth > today.month) {
                  selectedMonth = today.month;
                }
                showingYears = false;
              });
            },
            child: Container(
              decoration: BoxDecoration(
                color: isSelected ? Colors.blue : Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: isSelected ? Colors.blue : Colors.grey.shade300,
                  width: 1,
                ),
              ),
              child: Center(
                child: Text(
                  year.toString(),
                  style: TextStyle(
                    fontSize: isMobile ? 12 : 14,
                    fontWeight: FontWeight.w500,
                    color: isSelected ? Colors.white : Colors.black,
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildMonthGrid(bool isMobile, bool isTablet) {
    final maxMonth = selectedYear < today.year ? 12 : today.month;

    // Responsive grid configuration
    int crossAxisCount = 3;
    double childAspectRatio = 2.5;
    
    if (isMobile) {
      crossAxisCount = 2;
      childAspectRatio = 2.2;
    } else if (isTablet) {
      crossAxisCount = 3;
      childAspectRatio = 2.3;
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: crossAxisCount,
          childAspectRatio: childAspectRatio,
          crossAxisSpacing: 6,
          mainAxisSpacing: 6,
        ),
        shrinkWrap: true,
        physics: NeverScrollableScrollPhysics(),
        itemCount: maxMonth,
        itemBuilder: (context, index) {
          final month = index + 1;
          final isSelected = month == selectedMonth;

          return GestureDetector(
            onTap: () {
              setState(() {
                selectedMonth = month;
              });
            },
            child: Container(
              decoration: BoxDecoration(
                color: isSelected ? Colors.blue : Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: isSelected ? Colors.blue : Colors.grey.shade300,
                  width: 1,
                ),
              ),
              child: Center(
                child: Text(
                  DateFormat('MMM').format(DateTime(selectedYear, month)),
                  style: TextStyle(
                    fontSize: isMobile ? 12 : 14,
                    fontWeight: FontWeight.w500,
                    color: isSelected ? Colors.white : Colors.black,
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildActionButtons(bool isMobile) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Expanded(
            child: TextButton(
              onPressed: widget.onCancel,
              style: TextButton.styleFrom(
                padding: EdgeInsets.symmetric(
                  horizontal: isMobile ? 16 : 24, 
                  vertical: isMobile ? 10 : 12,
                ),
                backgroundColor: Colors.grey.shade200,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'Cancel',
                style: TextStyle(
                  fontSize: isMobile ? 12 : 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.black,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: TextButton(
              onPressed: () {
                widget.onDateSelected(DateTime(selectedYear, selectedMonth, 1));
              },
              style: TextButton.styleFrom(
                padding: EdgeInsets.symmetric(
                  horizontal: isMobile ? 16 : 32, 
                  vertical: isMobile ? 10 : 12,
                ),
                backgroundColor: Colors.blue,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'SET',
                style: TextStyle(
                  fontSize: isMobile ? 12 : 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  double _calculatePickerWidth(double screenWidth) {
    if (screenWidth <= 480) {
      return (screenWidth - 40).clamp(280, screenWidth - 20);
    } else if (screenWidth <= 800) {
      return (screenWidth * 0.85).clamp(300, 450);
    } else {
      return 350;
    }
  }

  double _calculatePickerHeight(double screenHeight) {
    if (screenHeight <= 600) {
      return (screenHeight * 0.8).clamp(300, 400);
    } else {
      return 380;
    }
  }
}
